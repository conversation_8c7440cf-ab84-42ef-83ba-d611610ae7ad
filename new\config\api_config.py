#!/usr/bin/env python3
"""
API配置管理模块
统一管理所有API密钥和配置
"""

import os
from typing import Optional
from dotenv import load_dotenv
import logging

logger = logging.getLogger(__name__)

# 加载 .env 文件
load_dotenv()

class APIConfig:
    """API配置管理类"""
    
    def __init__(self):
        self._load_config()
    
    def _load_config(self):
        """加载配置"""
        # OpenAI 配置
        self.openai_api_key = os.getenv(
            "OPENAI_API_KEY", 
            "sk-qRnt09M8Ncvl0Dwk1aA374833a6a4dB5B6A6De2e1901D146"
        )
        self.openai_api_base = os.getenv(
            "OPENAI_API_BASE", 
            "https://api.shubiaobiao.cn/v1"
        )
        
        # Google 配置
        self.google_api_key = os.getenv(
            "GOOGLE_API_KEY", 
            self.openai_api_key  # 默认使用OpenAI的key
        )
        
        # 代理配置
        self.http_proxy = os.getenv("HTTP_PROXY")
        self.https_proxy = os.getenv("HTTPS_PROXY")
        
        # 验证配置
        self._validate_config()
    
    def _validate_config(self):
        """验证配置有效性"""
        if not self.openai_api_key:
            logger.warning("OPENAI_API_KEY not set, using default")
        
        if not self.google_api_key:
            logger.warning("GOOGLE_API_KEY not set, using OpenAI key as fallback")
    
    def get_openai_config(self) -> dict:
        """获取OpenAI配置"""
        return {
            "api_key": self.openai_api_key,
            "base_url": self.openai_api_base
        }
    
    def get_google_config(self) -> dict:
        """获取Google配置"""
        return {
            "api_key": self.google_api_key,
            "base_url": self.openai_api_base  # Google也使用相同的base_url
        }
    
    def get_proxy_config(self) -> dict:
        """获取代理配置"""
        config = {}
        if self.http_proxy:
            config["http"] = self.http_proxy
        if self.https_proxy:
            config["https"] = self.https_proxy
        return config
    
    def set_proxy(self):
        """设置系统代理环境变量"""
        if self.http_proxy:
            os.environ["HTTP_PROXY"] = self.http_proxy
            os.environ["http_proxy"] = self.http_proxy
        if self.https_proxy:
            os.environ["HTTPS_PROXY"] = self.https_proxy
            os.environ["https_proxy"] = self.https_proxy

# 全局配置实例
_global_config = None

def get_api_config() -> APIConfig:
    """获取全局API配置实例"""
    global _global_config
    if _global_config is None:
        _global_config = APIConfig()
    return _global_config

# 便捷函数
def get_openai_api_key() -> str:
    """获取OpenAI API Key"""
    return get_api_config().openai_api_key

def get_openai_base_url() -> str:
    """获取OpenAI Base URL"""
    return get_api_config().openai_api_base

def get_google_api_key() -> str:
    """获取Google API Key"""
    return get_api_config().google_api_key
